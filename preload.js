const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
  // API配置
  updateApiConfig: (config) => ipcRenderer.invoke('update-api-config', config),
  
  // 爬虫功能
  getArticlesFromSite: (url) => ipcRenderer.invoke('get-articles-from-site', url),
  processArticle: (article) => ipcRenderer.invoke('process-article', article),
  
  // 报告生成
  generateReport: (articles) => ipcRenderer.invoke('generate-report', articles),
  saveResults: (results) => ipcRenderer.invoke('save-results', results),
  
  // 主题
  getTheme: () => ipcRenderer.invoke('get-theme'),
  
  // 平台信息
  platform: process.platform
}); 