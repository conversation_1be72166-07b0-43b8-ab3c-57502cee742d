const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const NewsScraperAPI = require('./src/api/scraper');

// 定义CSS变量
const themes = {
  primary: '#1976d2',
  secondary: '#424242',
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  background: '#f5f5f5',
  surface: '#ffffff',
  text: '#212121',
  textSecondary: '#757575'
};

let mainWindow;
let scraperAPI;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: '新闻爬取工具',
    show: false
  });

  mainWindow.loadFile('src/renderer/index.html');

  // 开发模式下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // 窗口准备就绪后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 初始化爬虫API
  scraperAPI = new NewsScraperAPI();
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 处理程序
ipcMain.handle('update-api-config', async (event, config) => {
  try {
    scraperAPI.updateConfig(config);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-articles-from-site', async (event, url) => {
  try {
    const articles = await scraperAPI.getArticlesFromSite(url);
    return { success: true, articles };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('process-article', async (event, article) => {
  try {
    const processedArticle = await scraperAPI.processArticle(article);
    
    if (processedArticle) {
      return { success: true, article: processedArticle };
    } else {
      return { success: false, error: '文章处理失败：不是有效的文章或无法提取内容' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-report', async (event, articles) => {
  try {
    const reportContent = generateReportContent(articles);
    const outputDir = path.join(require('os').homedir(), 'Desktop');
    const today = scraperAPI.getToday();
    const outputPath = path.join(outputDir, `${today}.txt`);
    
    await fs.writeFile(outputPath, reportContent, 'utf-8');
    
    // 同时保存到生成结果目录
    const resultDir = path.join(__dirname, '..', '生成结果');
    try {
      await fs.mkdir(resultDir, { recursive: true });
      await fs.writeFile(path.join(resultDir, `${today}.txt`), reportContent, 'utf-8');
    } catch (err) {
      console.warn('保存到生成结果目录失败:', err.message);
    }
    
    return { success: true, outputPath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('save-results', async (event, results) => {
  try {
    const { processedArticles, failedArticles, summary } = results;
    
    // 生成保存内容
    const timestamp = new Date().toLocaleString('zh-CN').replace(/[:/\s]/g, '-');
    const fileName = `处理结果-${timestamp}.json`;
    
    // 创建保存目录
    const saveDir = path.join(require('os').homedir(), 'Desktop', '新闻处理结果');
    await fs.mkdir(saveDir, { recursive: true });
    
    const filePath = path.join(saveDir, fileName);
    
    // 格式化保存数据
    const saveData = {
      summary: {
        ...summary,
        savedAt: new Date().toISOString(),
        version: '1.0'
      },
      processedArticles: processedArticles.map(article => ({
        title: article.title,
        origin: article.origin,
        date: article.date,
        url: article.url,
        content: article.content,
        tag: article.tag,
        processedData: article.processedData,
        processedAt: article.processedAt
      })),
      failedArticles: failedArticles.map(article => ({
        title: article.title,
        origin: article.origin,
        date: article.date,
        url: article.url,
        error: article.error,
        failedAt: article.failedAt || article.retryFailedAt
      }))
    };
    
    // 保存JSON文件
    await fs.writeFile(filePath, JSON.stringify(saveData, null, 2), 'utf-8');
    
    // 同时生成可读的文本报告
    const reportContent = generateProcessResultReport(saveData);
    const reportFileName = `处理报告-${timestamp}.txt`;
    const reportPath = path.join(saveDir, reportFileName);
    await fs.writeFile(reportPath, reportContent, 'utf-8');
    
    return { 
      success: true, 
      filePath: filePath,
      reportPath: reportPath
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

function generateReportContent(articles) {
  const numberToChinese = (num) => {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    if (num <= 10) {
      return chineseNums[num];
    } else if (num < 20) {
      return `十${num > 10 ? chineseNums[num - 10] : ''}`;
    } else {
      return `${chineseNums[Math.floor(num / 10)]}十${num % 10 ? chineseNums[num % 10] : ''}`;
    }
  };

  let reportContent = "";
  articles.forEach((article, index) => {
    const num = index + 1;
    reportContent += (
      `（${numberToChinese(num)}）${article.title}` +
      `（来自：《${article.origin}》，发布时间：${article.date}）\n` +
      `${article.content}\n\n`
    );
  });

  return reportContent;
}

function generateProcessResultReport(saveData) {
  const { summary, processedArticles, failedArticles } = saveData;
  
  let report = "=".repeat(60) + "\n";
  report += "                    新闻处理结果报告\n";
  report += "=".repeat(60) + "\n\n";
  
  // 处理摘要
  report += "【处理摘要】\n";
  report += `处理时间：${new Date(summary.processedAt).toLocaleString('zh-CN')}\n`;
  report += `保存时间：${new Date(summary.savedAt).toLocaleString('zh-CN')}\n`;
  report += `总选择文章：${summary.totalSelected} 篇\n`;
  report += `成功处理：${summary.successCount} 篇\n`;
  report += `处理失败：${summary.failCount} 篇\n`;
  report += `成功率：${((summary.successCount / summary.totalSelected) * 100).toFixed(1)}%\n\n`;
  
  // 成功处理的文章
  if (processedArticles.length > 0) {
    report += "=".repeat(60) + "\n";
    report += "【成功处理的文章】\n";
    report += "=".repeat(60) + "\n\n";
    
    processedArticles.forEach((article, index) => {
      report += `${index + 1}. ${article.title}\n`;
      report += `   来源：${article.origin}\n`;
      report += `   日期：${article.date}\n`;
      report += `   标签：${article.tag || '未分类'}\n`;
      report += `   链接：${article.url || '未提供'}\n`;
      report += `   处理时间：${new Date(article.processedAt).toLocaleString('zh-CN')}\n`;
      if (article.processedData) {
        report += `   处理结果：${typeof article.processedData === 'string' ? article.processedData : JSON.stringify(article.processedData)}\n`;
      }
      report += `   内容：${article.content ? article.content : '无内容'}\n\n`;
    });
  }
  
  // 失败的文章
  if (failedArticles.length > 0) {
    report += "=".repeat(60) + "\n";
    report += "【处理失败的文章】\n";
    report += "=".repeat(60) + "\n\n";
    
    failedArticles.forEach((article, index) => {
      report += `${index + 1}. ${article.title}\n`;
      report += `   来源：${article.origin}\n`;
      report += `   日期：${article.date}\n`;
      report += `   链接：${article.url || '未提供'}\n`;
      report += `   失败时间：${new Date(article.failedAt).toLocaleString('zh-CN')}\n`;
      report += `   失败原因：${article.error}\n\n`;
    });
  }
  
  report += "=".repeat(60) + "\n";
  report += "                      报告结束\n";
  report += "=".repeat(60) + "\n";
  
  return report;
}

// 获取主题配置
ipcMain.handle('get-theme', async () => {
  return themes;
}); 